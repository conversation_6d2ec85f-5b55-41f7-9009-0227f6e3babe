import { defineStore } from 'pinia';
import { getExistSpecies } from '/@/api/screen';

export interface ExistSpeciesItem {
	name: string;
	eventType: number | null;
	[key: string]: any;
}

export interface SpeciesFilterOptions {
	name?: string;
	eventType?: number | string;
	eventTypes?: (number | string)[];
}

export interface ExistSpeciesState {
	species: ExistSpeciesItem[];
	loading: boolean;
	error: string | null;
	lastFetchTime: number; // 上次获取数据的时间戳
	cacheExpireTime: number; // 缓存过期时间（毫秒）
	refreshTimer: number | null; // 定时器ID
}

/**
 * 存在物种管理 Store
 * @methods ensureSpecies 确保物种数据存在（智能缓存）
 * @methods fetchSpecies 强制获取物种列表
 * @methods getFilteredSpecies 根据条件过滤物种
 * @methods startAutoRefresh 启动自动刷新
 * @methods stopAutoRefresh 停止自动刷新
 */
export const useExistSpeciesStore = defineStore('existSpecies', {
	state: (): ExistSpeciesState => ({
		species: [],
		loading: false,
		error: null,
		lastFetchTime: 0,
		cacheExpireTime: 1 * 60 * 1000, // 1分钟缓存过期时间
		refreshTimer: null,
	}),

	getters: {
		// 判断缓存是否已过期
		isCacheExpired: (state): boolean => {
			const now = Date.now();
			return now - state.lastFetchTime > state.cacheExpireTime;
		},

		// 判断是否有有效数据
		hasValidData: (state): boolean => {
			return state.species.length > 0;
		},

		// 获取所有物种名称列表（去重）
		allSpeciesNames: (state): string[] => {
			const names = state.species.map((item) => item.name).filter(Boolean);
			return [...new Set(names)];
		},
	},

	actions: {
		/**
		 * 确保物种数据存在（智能缓存）
		 * 如果没有数据或缓存过期，则自动获取
		 */
		async ensureSpecies(): Promise<void> {
			// 如果正在加载，直接返回
			if (this.loading) {
				return;
			}

			// 如果有有效数据且缓存未过期，直接返回
			if (this.hasValidData && !this.isCacheExpired) {
				return;
			}

			// 获取数据
			await this.fetchSpecies();
		},

		/**
		 * 获取物种列表（强制刷新）
		 */
		async fetchSpecies(): Promise<void> {
			this.loading = true;
			this.error = null;

			try {
				const response = await getExistSpecies();

				if (response.status === 200 && response.payload) {
					this.species = response.payload;
					this.lastFetchTime = Date.now();
				}
			} catch (error) {
				console.error('获取存在物种失败:', error);
				this.error = '获取存在物种失败';
			} finally {
				this.loading = false;
			}
		},

		/**
		 * 根据条件过滤物种
		 * @param name 物种名称关键字
		 * @param eventTypeOrTypes 事件类型
		 * @returns 过滤后的物种列表
		 */
		getFilteredSpecies(
			name: string,
			eventTypeOrTypes?: number | string | (number | string)[]
		): ExistSpeciesItem[] {
			let filtered = [...this.species];

			// 根据 eventType 过滤
			if (!eventTypeOrTypes && eventTypeOrTypes !== 'all') {
				let targetEventTypes: number[] = [];

				if (Array.isArray(eventTypeOrTypes)) {
					// 处理数组：过滤掉 'all' 和非数字值
					targetEventTypes = eventTypeOrTypes.filter(
						(type): type is number => typeof type === 'number'
					);
				} else if (typeof eventTypeOrTypes === 'number') {
					// 处理单个数值
					targetEventTypes = [eventTypeOrTypes];
				}

				if (targetEventTypes.length > 0) {
					filtered = filtered.filter(
						(species) => species.eventType !== null && targetEventTypes.includes(species.eventType)
					);
				}
			}

			// 根据输入的名称进行模糊匹配过滤
			if (name && name.trim().length > 0) {
				const nameKeyword = name.trim().toLowerCase();
				filtered = filtered.filter(
					(species) => species.name && species.name.toLowerCase().includes(nameKeyword)
				);
			}

			return filtered;
		},

		/**
		 * 启动自动刷新（1分钟间隔）
		 */
		startAutoRefresh(): void {
			// 如果已经有定时器在运行，先清除
			this.stopAutoRefresh();

			// 立即获取一次数据
			this.ensureSpecies();

			// 设置定时器，每分钟刷新一次
			this.refreshTimer = window.setInterval(() => {
				console.log('定时刷新物种数据...');
				this.fetchSpecies();
			}, this.cacheExpireTime);

			console.log('物种数据自动刷新已启动（1分钟间隔）');
		},

		/**
		 * 停止自动刷新
		 */
		stopAutoRefresh(): void {
			if (this.refreshTimer !== null) {
				clearInterval(this.refreshTimer);
				this.refreshTimer = null;
				console.log('物种数据自动刷新已停止');
			}
		},

		/**
		 * 重置状态
		 */
		resetState(): void {
			this.stopAutoRefresh();
			this.species = [];
			this.loading = false;
			this.error = null;
			this.lastFetchTime = 0;
		},

		/**
		 * 设置缓存过期时间
		 */
		setCacheExpireTime(milliseconds: number): void {
			this.cacheExpireTime = milliseconds;

			// 如果正在自动刷新，重新启动以应用新的间隔时间
			if (this.refreshTimer !== null) {
				this.startAutoRefresh();
			}
		},
	},

	// 开启数据持久化
	persist: {
		key: 'exist-species-store',
		paths: ['species', 'lastFetchTime'], // 持久化物种数据和最后获取时间
	},
});

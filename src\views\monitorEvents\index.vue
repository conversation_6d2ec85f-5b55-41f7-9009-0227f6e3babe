<template>
	<div class="layout-pd">
		<ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
				<!-- 设备相关筛选 -->
				<el-form-item label="设备类型">
					<el-radio-group v-model="state.tableData.filter.deviceType">
						<el-radio label="">全部</el-radio>
						<el-radio :label="1">红外设备</el-radio>
						<el-radio :label="0">监控设备</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="设备">
					<el-select v-model="state.tableData.filter.num" placeholder="请选择设备" clearable>
						<el-option
							v-for="item in deviceOptions"
							:key="item.id"
							:value="item.num"
							:label="`设备编号：${item.num}，名称：${item.name}`"
						/>
					</el-select>
				</el-form-item>
				<br />

				<!-- 识别相关筛选 -->
				<el-form-item v-if="showIdentifyType" label="识别类型">
					<el-radio-group
						v-model="state.tableData.filter.speciesEventType"
						@change="onSpeciesEventTypeChange"
					>
						<el-radio
							v-for="item in animalTypesStore.screenAnimalTypeOptions"
							:key="item.value"
							:label="item.value"
						>
							{{ item.label }}
						</el-radio>
					</el-radio-group>

					<!-- <el-checkbox-group
						v-model="state.tableData.filter.speciesEventTypes"
						@change="onEventTypesChange"
					>
						<el-checkbox
							v-for="item in animalTypesStore.screenAnimalTypeOptions"
							:key="item.value"
							:label="item.value"
						>
							{{ item.label }}
						</el-checkbox>
					</el-checkbox-group> -->
				</el-form-item>
				<!-- 物种名称筛选项 -->
				<el-form-item v-if="showSpeciesName" label="物种名称">
					<el-select
						v-model="state.tableData.filter.name"
						placeholder="请输入选择物种"
						filterable
						remote
						:remote-method="handleSpeciesSearch"
						clearable
						style="width: 240px"
					>
						<el-option
							v-for="item in speciesOptions"
							:key="item.name"
							:label="item.name"
							:value="item.name"
						/>
					</el-select>
				</el-form-item>
				<br v-if="showIdentifyType || showSpeciesName" />

				<!-- 其他筛选 -->
				<el-form-item label="开始时间">
					<el-date-picker
						v-model="state.tableData.filter.startTime"
						type="datetime"
						format="YYYY-MM-DD HH:mm:ss"
						value-format="YYYY-MM-DD HH:mm:ss"
						:disabled-date="startPickerOptions"
						placeholder="开始时间"
					/>
				</el-form-item>
				<el-form-item label="结束时间">
					<el-date-picker
						v-model="state.tableData.filter.endTime"
						type="datetime"
						format="YYYY-MM-DD HH:mm:ss"
						value-format="YYYY-MM-DD HH:mm:ss"
						:default-time="endDefaultTime"
						:disabled-date="endPickerOptions"
						placeholder="结束时间"
					/>
				</el-form-item>
			</template>

			<template #searchBtns>
				<el-button type="info" @click="onSelectAll">全选</el-button>
				<el-button type="danger" :disabled="selectIds.length === 0" @click="onBatchDelete">
					<template #icon>
						<el-icon><ele-Delete /></el-icon>
					</template>
					删除
				</el-button>
				<!-- 仅生物多样性子分类 `groupEventType = 0` 支持导出 -->
				<!-- <el-button type="primary" @click="onExport">
					<template #icon>
						<el-icon><ele-Download /></el-icon>
					</template>
					导出
				</el-button> -->
			</template>
		</ViewSearch>

		<el-row class="atlas" v-loading="state.tableData.loading" :gutter="20">
			<template v-if="state.tableData.data.length > 0">
				<el-col
					:xs="24"
					:sm="12"
					:md="8"
					:lg="4"
					:xl="4"
					v-for="(item, $index) in state.tableData.data"
					:key="item.id"
				>
					<el-card class="atlas-item" :key="item.id">
						<el-checkbox v-model="item.checked" />
						<div class="thumb-rec">
							<el-image
								class="thumbnail"
								:src="item.pictureUrl || item.oriPictureUrl || item.oriPictureThumbnailUrl"
								fit="contain"
								lazy
								@click="handleImagePreview($index)"
								style="cursor: pointer"
							></el-image>
							<div
								class="recResult-content"
								v-if="item.monitorEventDetails && item.monitorEventDetails.length"
							>
								<span
									class="s-link"
									v-for="res in item.monitorEventDetails"
									:key="res.recResult"
									@click="onAssSpecies(res.recResult)"
								>
									{{ res.recResult }}
									{{ res.recResultCnt && `（${res.recResultCnt}）` }}
								</span>
							</div>
						</div>

						<div class="other-content">
							<div>
								<el-tooltip :hide-after="0">
									{{ item.device.name
									}}{{ item.device.channelName ? ` [${item.device.channelName}]` : '' }}
									<template #content>
										{{ item.device.name
										}}{{ item.device.channelName ? ` [${item.device.channelName}]` : '' }}
									</template>
								</el-tooltip>
							</div>
							<div class="mt5">
								<el-tooltip :disabled="!item.longitude" :hide-after="0">
									GPS：{{
										item.longitude && item.latitude ? `${item.longitude},${item.latitude}` : '-'
									}}
									<template #content>
										{{
											item.longitude && item.latitude ? `${item.longitude},${item.latitude}` : '-'
										}}
									</template>
								</el-tooltip>
							</div>
							<div class="mt5">{{ item.recTime }}</div>
							<div class="operate-btns" v-auths="['*:*:*', 'monitor-event-files:*:*']">
								<el-button type="primary" text @click="onEdit(item)">
									<el-icon><ele-EditPen /></el-icon>
								</el-button>
								<el-button type="danger" text @click="onDelete(item.id)">
									<el-icon><ele-Delete /></el-icon>
								</el-button>
							</div>
							<!-- 右下角内容 -->
							<div class="targetType">
								<el-link class="font12" type="primary" @click="onTargetTypeClick(item)">
									原图
								</el-link>
							</div>
						</div>
					</el-card>
				</el-col>
			</template>
			<el-empty class="flex-margin" v-else></el-empty>
		</el-row>
		<el-pagination
			class="mt10"
			v-model:current-page="state.tableData.pageParams.page"
			:page-sizes="[12, 24, 38, 48]"
			background
			v-model:page-size="state.tableData.pageParams.size"
			layout="total, sizes, prev, pager, next, jumper"
			:total="state.tableData.total"
			@size-change="onHandleSizeChange"
			@current-change="onHandleCurrentChange"
		>
		</el-pagination>

		<EditDialog ref="editDialogRef"></EditDialog>
		<SpeciesDialog ref="speciesDialogRef"></SpeciesDialog>

		<!-- 图片预览组件 -->
		<ImgPreview
			ref="imgPreviewRef"
			:perviewPicList="perviewPicList"
			:pageSize="state.tableData.pageParams.size"
			:loading="state.tableData.loading"
			:data="state.tableData"
			maskLoading
			:total="state.tableData.total"
			:currentPage="state.tableData.pageParams.page"
			@loadNextPage="handleLoadNextPage"
			@loadPrevPage="handleLoadPrevPage"
			correctShow
			@correct="handleCorrect"
		></ImgPreview>

		<div v-if="dragData.show" ref="dragRef" v-drag class="viewer-remark">
			{{ dragData.remark }}
		</div>
	</div>
</template>

<script setup lang="ts" name="MonitorEvents">
import {
	ref,
	reactive,
	onMounted,
	onBeforeUnmount,
	computed,
	defineAsyncComponent,
	nextTick,
} from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { writeText } from 'clipboard-polyfill';
import {
	getMonitorEvents,
	getMonitorEventTypes,
	deleteMonitorEvent,
	batchDeleteMonitorEvent,
	revisedEvent,
	exportMonitorFiles,
	getEventTypes,
	getUnknownSpecies,
} from '/@/api/monitorEvents';
import { creadteMoments, deleteByEventId } from '/@/api/moments';
import { getFireDevices } from '/@/api/devices';
import { useDevices } from '/@/hooks/useDevices';
import { Bird_Tag } from '/@/utils/constants';
import { useAnimalTypesStore } from '/@/stores/animalTypes';
import { useExistSpeciesStore } from '/@/stores/existSpecies';
import { useSpeciesSearch } from '/@/hooks/useSpeciesSearch';
import mittBus from '/@/utils/mitt';
getUnknownSpecies({}).then();
const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
const EditDialog = defineAsyncComponent(() => import('./editDialog.vue'));
const SpeciesDialog = defineAsyncComponent(() => import('./speciesDialog.vue'));
// 导入 ImgPreview 组件
const ImgPreview = defineAsyncComponent(() => import('/@/components/ImgPreview.vue'));

// 使用动物类型 store
const animalTypesStore = useAnimalTypesStore();
// 使用存在物种 store
const existSpeciesStore = useExistSpeciesStore();
// 设置结束日期的默认时间为 23:59:59
const endDefaultTime = ref(new Date(2000, 1, 1, 23, 59, 59));
// 使用物种搜索 hook
const { speciesOptions, searchExistSpecies, clearSearchResults } = useSpeciesSearch();

// 定义变量内容
const devices = useDevices(); // 红外、监控、巡护设备
const fireDevices = ref(); // 防火设备
const state = reactive<ViewBaseState<MonitorEventRow>>({
	tableData: {
		filter: {
			deviceType: '',
			num: '',
			groupEventType: 0,
			speciesEventType: 'all', // 识别类型：默认选中全部
			name: '', // 物种名称模糊匹配
			startTime: '',
			endTime: '',
			sort: 'recTime,desc',
		},
		data: [],
		loading: false,
		pageParams: {
			page: 1,
			size: 12,
		},
		total: 0,
	},
	selectAll: false,
});

const startPickerOptions = () => {
	const endTime = state.tableData.filter.endTime;
	if (endTime) {
		const startTime = new Date(state.tableData.filter.startTime);
		// console.log('startTime', startTime.getHours());
		if (startTime.getHours() || startTime.getMinutes() || startTime.getSeconds()) {
			// 如果 startTime 包含时分秒，则 endTime 不能选择当天
			// const oneDayBefore = new Date(time);
			// oneDayBefore.setDate(oneDayBefore.getDate() - 1);
			// oneDayBefore.setHours(23, 59, 59, 999); // 设置为前一天的23:59:59.999
			// return time.getTime() <= oneDayBefore.getTime();
		}
		// return time.getTime() > new Date(state.tableData.filter.endTime).getTime();
	}
};
const endPickerOptions = (time: Date) => {
	if (state.tableData.filter.startTime) {
		return time.getTime() < new Date(state.tableData.filter.startTime).getTime();
	}
};

// 根据设备类型deviceType过滤，并去重（即同一设备多通道仅展示一个）
const deviceOptions = computed(() => {
	const devChannel = devices.data.value;
	const devChannelNum = devChannel.map((item) => item.num);
	const filterDevChannel = devChannel.filter(
		(item, $index) => devChannelNum.indexOf(item.num) === $index
	);
	if (state.tableData.filter.deviceType === 1) {
		return filterDevChannel.filter((item: DeviceRow) => item.sourceType === 2);
	} else if (state.tableData.filter.deviceType === 0) {
		return filterDevChannel.filter(
			(item: DeviceRow) => item.sourceType === 1 || item.sourceType === 3
		);
	} else if (state.tableData.filter.deviceType === 4) {
		return filterDevChannel.filter((item: DeviceRow) => item.sourceType === 4);
	} else if (state.tableData.filter.deviceType === 5) {
		return fireDevices.value;
	}
	return filterDevChannel;
});
// 页面加载时
onMounted(async () => {
	// 确保动物类型数据存在（智能缓存）
	await animalTypesStore.ensureAnimalTypes();

	// 启动物种数据的自动刷新（1分钟间隔）
	existSpeciesStore.startAutoRefresh();

	// 注册mitt事件监听器 - 保持当前筛选条件和页码
	mittBus.on('monitorEventRefresh', () => {
		onRefresh(false, false); // 不重置页码，不重置筛选条件
	});

	initFireDevices();
	getTableData();
});

// 组件卸载时清理定时器
onBeforeUnmount(() => {
	existSpeciesStore.stopAutoRefresh();
	// 移除mitt事件监听器
	mittBus.off('monitorEventRefresh');
});

const props = defineProps({
	groupEventType: { type: Number },
	speciesEventType: { type: Number },
});

// 根据事件分类控制筛选项显示
const showIdentifyType = computed(() => {
	// 发现确定物种 和 发现未确定物种 显示识别类型
	return props.speciesEventType === 100 || props.speciesEventType === 101;
});

const showSpeciesName = computed(() => {
	// 仅发现确定物种显示物种名称
	return props.speciesEventType === 100;
});

// 初始化防火设备列表
const initFireDevices = async () => {
	const params = {
		page: 0,
		size: 10000,
	};
	const { payload } = await getFireDevices(params);
	fireDevices.value = payload.content;
};

// 初始化表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	const query: any = {
		page: state.tableData.pageParams.page - 1,
		size: state.tableData.pageParams.size,
		...state.tableData.filter, // 传递事件分类参数
	};

	// 处理speciesEventTypes 如果是all 就props.speciesEventType
	if (query.speciesEventType === 'all') {
		query.speciesEventTypes = [props.speciesEventType];
	} else {
		query.speciesEventTypes = [props.speciesEventType, query.speciesEventType];
	}
	delete query.speciesEventType;
	const { payload } = await getEventTypes(query);
	state.tableData.data = payload.content.map((item: MonitorEventRow) => ({
		...item,
		checked: false,
	}));
	state.tableData.total = payload.totalElements;
	state.selectAll = false;
	state.tableData.loading = false;
};
const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetPage) state.tableData.pageParams.page = 1;
	if (resetFilter) {
		state.tableData.filter.name = '';
		state.tableData.filter.deviceType = '';
		state.tableData.filter.num = '';
		state.tableData.filter.speciesEventType = 'all';
		state.tableData.filter.startTime = '';
		state.tableData.filter.endTime = '';
	}
	getTableData();
};
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getTableData();
};
const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getTableData();
};

// 处理识别类型单选框变化
const onSpeciesEventTypeChange = () => {
	// 清空物种名称和搜索结果
	state.tableData.filter.name = '';
	clearSearchResults();
};

// 处理识别类型复选框变化
const onEventTypesChange = (value: string[]) => {
	const nonAllOptions = animalTypesStore.animalTypeOptions.filter(
		(option) => option.value !== 'all' && option.value !== 24
	);
	const allOptionValues = nonAllOptions.map((option) => option.value);

	// 获取用户最后点击的选项（通常是数组的最后一个元素）
	const lastClicked = value.length > 0 ? value[value.length - 1] : null;

	// 1. 如果用户最后点击的是"全部"，设置为只选中"全部"
	if (lastClicked === 'all') {
		state.tableData.filter.speciesEventTypes = ['all'];
		// 清空物种名称和搜索结果
		state.tableData.filter.name = '';
		clearSearchResults();
		return;
	}

	// 2. 如果用户最后点击的是具体选项且当前包含"全部"，移除"全部"保留具体选项
	if (value.includes('all') && lastClicked !== 'all' && lastClicked !== null) {
		const specificOptions = value.filter((item) => item !== 'all');
		state.tableData.filter.speciesEventTypes = specificOptions;
		// 清空物种名称和搜索结果
		state.tableData.filter.name = '';
		clearSearchResults();
		return;
	}

	// 3. 如果选择了所有具体选项，自动切换为"全部"
	if (value.length === allOptionValues.length && allOptionValues.length > 0) {
		state.tableData.filter.speciesEventTypes = ['all'];
		// 清空物种名称和搜索结果
		state.tableData.filter.name = '';
		clearSearchResults();
		return;
	}

	// 4. 如果没有选择任何项，默认选择"全部"
	if (value.length === 0) {
		state.tableData.filter.speciesEventTypes = ['all'];
		// 清空物种名称和搜索结果
		state.tableData.filter.name = '';
		clearSearchResults();
		return;
	}
	// 5. 正常更新选择
	state.tableData.filter.speciesEventTypes = value;
	// 清空物种名称和搜索结果
	state.tableData.filter.name = '';
	clearSearchResults();
};

// 物种搜索包装函数，兼容 speciesEventTypes
const handleSpeciesSearch = async (name: string) => {
	// 直接传递 eventTypes 数组，让 searchExistSpecies 智能处理
	return await searchExistSpecies(name, state.tableData.filter.speciesEventTypes);
};

// # 大图预览
const perviewPicList = computed(() => {
	return state.tableData.data.map((item) => item.pictureUrl || item.oriPictureUrl);
});
const dragRef = ref();
const dragData = ref({
	remark: '',
	show: false,
});

// 全选
const onSelectAll = () => {
	state.selectAll = !state.selectAll;
	state.tableData.data = state.tableData.data.map((item) => ({
		...item,
		checked: state.selectAll,
	}));
};
const selectIds = computed(() => {
	return state.tableData.data.filter((item) => item.checked).map((item) => item.id);
});

// # 修改识别结果
const editDialogRef = ref<InstanceType<typeof EditDialog>>();
const onEdit = (row: MonitorEventRow) => {
	editDialogRef.value?.openDialog(row);
};

// # 图片预览相关
const imgPreviewRef = ref();

// 处理图片预览点击
const handleImagePreview = (index: number) => {
	imgPreviewRef.value?.handlePreview(index);
};

// 处理分页加载
const handleLoadNextPage = () => {
	if (state.tableData.pageParams.page * state.tableData.pageParams.size < state.tableData.total) {
		state.tableData.pageParams.page += 1;
		getTableData();
	}
};

const handleLoadPrevPage = () => {
	if (state.tableData.pageParams.page > 1) {
		state.tableData.pageParams.page -= 1;
		getTableData();
	}
};

// 处理订正功能（如果需要）
const handleCorrect = (index: number) => {
	// 根据需要实现订正逻辑
	console.log('订正图片索引:', index);
	onEdit(state.tableData.data[index]);
};

// # 导出
const onExport = () => {
	if (!state.tableData.filter.startTime && !state.tableData.filter.endTime) {
		ElMessage.warning('开始时间、结束时间必传');
		return;
	}

	if (isMoreThanAMonthApart(state.tableData.filter.startTime, state.tableData.filter.endTime)) {
		ElMessage.warning('开始时间、结束时间跨度不能超一个月');
		return;
	}
	const query = {
		deviceType: state.tableData.filter.deviceType,
		num: state.tableData.filter.num,
		eventTypes:
			state.tableData.filter.eventTypes.includes('all') ||
			state.tableData.filter.eventTypes.length === 0
				? []
				: state.tableData.filter.eventTypes,
		name: state.tableData.filter.name,
		startTime: state.tableData.filter.startTime,
		endTime: state.tableData.filter.endTime,
		speciesEventType: props.speciesEventType,
	};

	exportMonitorFiles(query).then((response: any) => {
		// console.log(response);
		let blob = new Blob([response], { type: 'application/vnd.ms-excel' });
		const downloadUrl = window.URL.createObjectURL(blob);
		const link = document.createElement('a');
		link.style.display = 'none';
		link.href = downloadUrl;
		link.setAttribute(
			'download',
			`${extractDate(query.startTime)}~${extractDate(query.endTime)}.xls`
		); // 可以自定义文件名
		document.body.appendChild(link);
		link.click();
		window.URL.revokeObjectURL(downloadUrl);
		document.body.removeChild(link);
	});
};
function isMoreThanAMonthApart(dateString1: string, dateString2: string) {
	const date1 = new Date(dateString1);
	const date2 = new Date(dateString2);

	const timeDiff = date2.getTime() - date1.getTime();
	const dayDiff = timeDiff / (1000 * 60 * 60 * 24);
	// 直接计算天数差
	return dayDiff > 30;
}
const extractDate = (dateTimeString: string) => {
	// 解析日期时间字符串为Date对象
	const dateObj = new Date(dateTimeString);
	// 获取年、月、日
	const year = dateObj.getFullYear();
	const month = String(dateObj.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1
	const day = String(dateObj.getDate()).padStart(2, '0'); // 日期
	// 组合成日期字符串
	return `${year}-${month}-${day}`;
};

// # 删除
const onDelete = (id: string) => {
	ElMessageBox.confirm(`此操作将永久删除，是否继续?`, '提示', {
		type: 'warning',
	})
		.then(async () => {
			await deleteMonitorEvent(id);
			getTableData();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};
const onBatchDelete = () => {
	ElMessageBox.confirm(`此操作将永久删除，是否继续?`, '提示', {
		type: 'warning',
	})
		.then(async () => {
			await batchDeleteMonitorEvent(selectIds.value);
			getTableData();
			ElMessage.success('批量删除成功');
		})
		.catch(() => {});
};

// # 识别结果链接物种百科
const speciesDialogRef = ref<InstanceType<typeof SpeciesDialog>>();
// 根据识别结果查询关联物种百科数据
const onAssSpecies = async (recResult: string) => {
	speciesDialogRef.value?.getAssSpecies(recResult);
};

// # 右下角根据事件区分模型
const targetMap = reactive<EmptyObjectType>({
	robu: 'r',
	gaoxinxing: 'g',
	alarm: 'g',
	aigc: 'ai',
	check: 'check',
	fire_alarm: 'h',
});
const getTargetType = (targetType: null | string) => {
	// 生物多样性没有targetType, 是鲁朗的模型默认r
	if (props.groupEventType === 0) {
		return 'r';
	}
	return targetType ? targetMap[targetType] : targetType;
};
// 数据来源为`monitorEventDetails`中的remark；若monitorEventDetails数据集为多条，合并remark；
const getTargetTypeRemark = (item: MonitorEventRow) => {
	if (item.monitorEventDetails.length) {
		let mergeRemark = '';
		item.monitorEventDetails.forEach((med) => {
			if (med.remark) mergeRemark += med.remark + '<br/>';
		});
		return mergeRemark;
	}
	return '';
};
const onTargetTypeClick = async (item: MonitorEventRow) => {
	try {
		writeText(item.oriPictureUrl);
		ElMessage.success('原图复制成功');
	} catch (err) {
		ElMessage.error('原图复制失败');
	}
};
</script>

<style scoped lang="scss">
@import '../../theme/atlas.scss';

.other-content {
	position: relative;

	.targetType {
		position: absolute;
		right: 10px;
		bottom: 10px;
	}
}

// 搜索重置按钮与第一行筛选项对齐
// ::v-deep(.rightFiltrate) {
// 	margin-top: calc(-64px + -30px) !important;
// }
</style>
